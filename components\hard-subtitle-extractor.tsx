"use client"

import React, { useState, use<PERSON><PERSON>back, useR<PERSON>, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { 
  Upload, 
  Play, 
  Pause, 
  Settings, 
  Download, 
  Plus, 
  Trash2, 
  ScanText,
  FileVideo,
  Clock,
  Languages
} from "lucide-react"
import { cn } from "@/lib/utils"

// 类型定义
interface VideoInfo {
  path: string
  duration: number
  fps: number
  width: number
  height: number
  currentTime: number
}

interface SubtitleAnchor {
  id: string
  centerX: number
  centerY: number
  height: number
  language: string
  isPrimary: boolean
  color: string
}

interface SubtitleResult {
  id: string
  startTime: number
  endTime: number
  text: string[]
  anchorId: string
}

interface OCRProgress {
  isRunning: boolean
  progress: number
  currentTime: number
  totalTime: number
  speed: number
}

// 语言选项
const LANGUAGE_OPTIONS = [
  { value: "zh", label: "中文" },
  { value: "en", label: "英语" },
  { value: "ja", label: "日语" },
  { value: "ko", label: "韩语" }
]

// 锚点颜色
const ANCHOR_COLORS = [
  "#ef4444", "#3b82f6", "#10b981", "#f59e0b",
  "#8b5cf6", "#ec4899", "#06b6d4", "#84cc16"
]

// 时间格式化函数
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}

export interface HardSubtitleExtractorProps {
  onOpenGlobalSettings?: (section?: string) => void
}

export default function HardSubtitleExtractor({ onOpenGlobalSettings }: HardSubtitleExtractorProps) {
  // 状态管理
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null)
  const [anchors, setAnchors] = useState<SubtitleAnchor[]>([])
  const [subtitleResults, setSubtitleResults] = useState<SubtitleResult[]>([])
  const [ocrProgress, setOcrProgress] = useState<OCRProgress>({
    isRunning: false,
    progress: 0,
    currentTime: 0,
    totalTime: 0,
    speed: 1
  })
  const [selectedAnchor, setSelectedAnchor] = useState<string | null>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  
  // 设置参数
  const [fps, setFps] = useState(10)
  const [minDuration, setMinDuration] = useState(500)
  const [autoAnalyze, setAutoAnalyze] = useState(false)
  
  // 文件拖拽处理
  const handleFileDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    const videoFile = files.find(file => file.type.startsWith('video/'))
    
    if (videoFile) {
      // 模拟视频信息加载
      const mockVideoInfo: VideoInfo = {
        path: videoFile.name,
        duration: 3600, // 1小时
        fps: 25,
        width: 1920,
        height: 1080,
        currentTime: 0
      }
      setVideoInfo(mockVideoInfo)
      setAnchors([])
      setSubtitleResults([])
      
      if (autoAnalyze) {
        startAnchorAnalysis()
      }
    }
  }, [autoAnalyze])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
  }, [])

  // 锚点分析
  const startAnchorAnalysis = () => {
    setOcrProgress(prev => ({ ...prev, isRunning: true, progress: 0 }))
    
    // 模拟分析过程
    const interval = setInterval(() => {
      setOcrProgress(prev => {
        const newProgress = prev.progress + 2
        if (newProgress >= 100) {
          clearInterval(interval)
          // 模拟生成锚点
          const mockAnchors: SubtitleAnchor[] = [
            {
              id: "anchor-1",
              centerX: 960,
              centerY: 900,
              height: 60,
              language: "zh",
              isPrimary: true,
              color: ANCHOR_COLORS[0]
            },
            {
              id: "anchor-2", 
              centerX: 960,
              centerY: 100,
              height: 40,
              language: "en",
              isPrimary: false,
              color: ANCHOR_COLORS[1]
            }
          ]
          setAnchors(mockAnchors)
          return { ...prev, isRunning: false, progress: 100 }
        }
        return { ...prev, progress: newProgress }
      })
    }, 100)
  }

  // 开始字幕提取
  const startSubtitleExtraction = () => {
    if (!videoInfo || anchors.length === 0) return
    
    setOcrProgress(prev => ({ ...prev, isRunning: true, progress: 0 }))
    setSubtitleResults([])
    
    // 模拟提取过程
    const interval = setInterval(() => {
      setOcrProgress(prev => {
        const newProgress = prev.progress + 1
        const currentTime = (newProgress / 100) * videoInfo.duration
        
        // 模拟生成字幕结果
        if (newProgress % 10 === 0 && newProgress < 100) {
          const mockSubtitle: SubtitleResult = {
            id: `subtitle-${Date.now()}`,
            startTime: currentTime,
            endTime: currentTime + 3,
            text: [`模拟字幕文本 ${Math.floor(newProgress / 10)}`],
            anchorId: anchors[0].id
          }
          setSubtitleResults(prev => [...prev, mockSubtitle])
        }
        
        if (newProgress >= 100) {
          clearInterval(interval)
          return { ...prev, isRunning: false, progress: 100 }
        }
        
        return { 
          ...prev, 
          progress: newProgress, 
          currentTime,
          speed: 10 + Math.random() * 5
        }
      })
    }, 200)
  }

  // 添加锚点
  const addAnchor = () => {
    const newAnchor: SubtitleAnchor = {
      id: `anchor-${Date.now()}`,
      centerX: 960,
      centerY: 540,
      height: 60,
      language: "zh",
      isPrimary: anchors.length === 0,
      color: ANCHOR_COLORS[anchors.length % ANCHOR_COLORS.length]
    }
    setAnchors(prev => [...prev, newAnchor])
  }

  // 删除锚点
  const removeAnchor = (id: string) => {
    setAnchors(prev => prev.filter(anchor => anchor.id !== id))
  }

  // 导出字幕
  const exportSubtitles = () => {
    if (subtitleResults.length === 0) return
    
    // 生成SRT格式
    let srtContent = ""
    subtitleResults.forEach((subtitle, index) => {
      const startTime = formatTime(subtitle.startTime).replace('.', ',')
      const endTime = formatTime(subtitle.endTime).replace('.', ',')
      srtContent += `${index + 1}\n${startTime} --> ${endTime}\n${subtitle.text.join('\n')}\n\n`
    })
    
    // 下载文件
    const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${videoInfo?.path.replace(/\.[^/.]+$/, '') || 'subtitles'}.srt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
        {/* 左侧：视频预览区域 */}
        <div className="lg:col-span-2 space-y-4">
          {/* 视频上传/预览区域 */}
          <Card>
            <CardContent className="p-6">
              {!videoInfo ? (
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-gray-400 transition-colors"
                  onDrop={handleFileDrop}
                  onDragOver={handleDragOver}
                >
                  <FileVideo className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium text-gray-600 mb-2">拖拽视频文件到此处</p>
                  <p className="text-sm text-gray-500">支持 MP4, AVI, MOV 等格式</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* 视频信息 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <FileVideo className="h-5 w-5" />
                      <span className="font-medium">{videoInfo.path}</span>
                    </div>
                    <Badge variant="secondary">
                      {formatTime(videoInfo.duration)}
                    </Badge>
                  </div>

                  {/* 模拟视频预览 */}
                  <div className="bg-black rounded-lg aspect-video flex items-center justify-center relative">
                    <div className="text-white text-center">
                      <Play className="h-16 w-16 mx-auto mb-2" />
                      <p>视频预览区域</p>
                      <p className="text-sm opacity-75">当前时间: {formatTime(videoInfo.currentTime)}</p>
                    </div>

                    {/* 字幕锚点覆盖层 */}
                    {anchors.map((anchor) => (
                      <div
                        key={anchor.id}
                        className={cn(
                          "absolute border-2 rounded cursor-pointer",
                          selectedAnchor === anchor.id ? "border-white" : "border-opacity-70"
                        )}
                        style={{
                          borderColor: anchor.color,
                          left: `${(anchor.centerX / videoInfo.width) * 100}%`,
                          top: `${(anchor.centerY / videoInfo.height) * 100}%`,
                          width: "100px",
                          height: `${anchor.height}px`,
                          transform: "translate(-50%, -50%)"
                        }}
                        onClick={() => setSelectedAnchor(anchor.id)}
                      >
                        <div
                          className="absolute -top-6 left-0 text-xs px-1 rounded text-white"
                          style={{ backgroundColor: anchor.color }}
                        >
                          {LANGUAGE_OPTIONS.find(lang => lang.value === anchor.language)?.label}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 时间轴控制 */}
                  <div className="space-y-2">
                    <Slider
                      value={[videoInfo.currentTime]}
                      max={videoInfo.duration}
                      step={0.1}
                      onValueChange={(value) => {
                        setVideoInfo(prev => prev ? { ...prev, currentTime: value[0] } : null)
                      }}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{formatTime(videoInfo.currentTime)}</span>
                      <span>{formatTime(videoInfo.duration)}</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 操作按钮区域 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={startAnchorAnalysis}
                  disabled={!videoInfo || ocrProgress.isRunning}
                  variant="outline"
                >
                  <ScanText className="h-4 w-4 mr-2" />
                  分析字幕锚点
                </Button>

                <Button
                  onClick={startSubtitleExtraction}
                  disabled={!videoInfo || anchors.length === 0 || ocrProgress.isRunning}
                >
                  <Play className="h-4 w-4 mr-2" />
                  开始提取
                </Button>

                <Button
                  onClick={addAnchor}
                  disabled={!videoInfo || ocrProgress.isRunning}
                  variant="outline"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加锚点
                </Button>

                <Button
                  onClick={exportSubtitles}
                  disabled={subtitleResults.length === 0}
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出字幕
                </Button>

                <Button
                  onClick={() => setShowSettings(!showSettings)}
                  variant="outline"
                  size="sm"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  设置
                </Button>
              </div>

              {/* 进度条 */}
              {ocrProgress.isRunning && (
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>提取进度</span>
                    <span className="text-blue-600">x{ocrProgress.speed.toFixed(1)}</span>
                  </div>
                  <Progress value={ocrProgress.progress} className="w-full" />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{formatTime(ocrProgress.currentTime)}</span>
                    <span>{ocrProgress.progress.toFixed(1)}%</span>
                  </div>
                </div>
              )}

              {/* 设置面板 */}
              {showSettings && (
                <div className="mt-4 p-4 border rounded-lg bg-gray-50 space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="auto-analyze"
                      checked={autoAnalyze}
                      onCheckedChange={(checked) => setAutoAnalyze(checked as boolean)}
                    />
                    <Label htmlFor="auto-analyze">拖入视频时自动分析字幕锚点</Label>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fps">检测频率 (FPS)</Label>
                      <Input
                        id="fps"
                        type="number"
                        value={fps}
                        onChange={(e) => setFps(Number(e.target.value))}
                        min={1}
                        max={60}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="min-duration">最短时间 (毫秒)</Label>
                      <Input
                        id="min-duration"
                        type="number"
                        value={minDuration}
                        onChange={(e) => setMinDuration(Number(e.target.value))}
                        min={100}
                        max={5000}
                      />
                    </div>
                  </div>

                  <Button
                    onClick={() => onOpenGlobalSettings?.('ocr-models')}
                    variant="outline"
                    size="sm"
                  >
                    配置OCR模型
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：锚点配置和字幕结果 */}
        <div className="space-y-4">
          <Tabs defaultValue="anchors" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="anchors">字幕锚点</TabsTrigger>
              <TabsTrigger value="results">提取结果</TabsTrigger>
            </TabsList>

            <TabsContent value="anchors" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Languages className="h-5 w-5 mr-2" />
                    字幕锚点配置
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    {anchors.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        <Languages className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>暂无字幕锚点</p>
                        <p className="text-sm">请先分析视频或手动添加</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {anchors.map((anchor, index) => (
                          <Card key={anchor.id} className={cn(
                            "p-4 cursor-pointer transition-colors",
                            selectedAnchor === anchor.id ? "ring-2 ring-blue-500" : ""
                          )}>
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <div
                                    className="w-4 h-4 rounded"
                                    style={{ backgroundColor: anchor.color }}
                                  />
                                  <span className="font-medium">锚点 {index + 1}</span>
                                  {anchor.isPrimary && (
                                    <Badge variant="default" className="text-xs">主字幕</Badge>
                                  )}
                                </div>
                                <Button
                                  onClick={() => removeAnchor(anchor.id)}
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-2 gap-2 text-sm">
                                <div>
                                  <Label>X坐标</Label>
                                  <Input
                                    type="number"
                                    value={anchor.centerX}
                                    onChange={(e) => {
                                      const newAnchors = anchors.map(a =>
                                        a.id === anchor.id
                                          ? { ...a, centerX: Number(e.target.value) }
                                          : a
                                      )
                                      setAnchors(newAnchors)
                                    }}
                                    className="h-8"
                                  />
                                </div>
                                <div>
                                  <Label>Y坐标</Label>
                                  <Input
                                    type="number"
                                    value={anchor.centerY}
                                    onChange={(e) => {
                                      const newAnchors = anchors.map(a =>
                                        a.id === anchor.id
                                          ? { ...a, centerY: Number(e.target.value) }
                                          : a
                                      )
                                      setAnchors(newAnchors)
                                    }}
                                    className="h-8"
                                  />
                                </div>
                                <div>
                                  <Label>高度</Label>
                                  <Input
                                    type="number"
                                    value={anchor.height}
                                    onChange={(e) => {
                                      const newAnchors = anchors.map(a =>
                                        a.id === anchor.id
                                          ? { ...a, height: Number(e.target.value) }
                                          : a
                                      )
                                      setAnchors(newAnchors)
                                    }}
                                    className="h-8"
                                  />
                                </div>
                                <div>
                                  <Label>语言</Label>
                                  <Select
                                    value={anchor.language}
                                    onValueChange={(value) => {
                                      const newAnchors = anchors.map(a =>
                                        a.id === anchor.id
                                          ? { ...a, language: value }
                                          : a
                                      )
                                      setAnchors(newAnchors)
                                    }}
                                  >
                                    <SelectTrigger className="h-8">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {LANGUAGE_OPTIONS.map(lang => (
                                        <SelectItem key={lang.value} value={lang.value}>
                                          {lang.label}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>

                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`primary-${anchor.id}`}
                                  checked={anchor.isPrimary}
                                  onCheckedChange={(checked) => {
                                    const newAnchors = anchors.map(a =>
                                      a.id === anchor.id
                                        ? { ...a, isPrimary: checked as boolean }
                                        : a
                                    )
                                    setAnchors(newAnchors)
                                  }}
                                />
                                <Label htmlFor={`primary-${anchor.id}`}>主字幕</Label>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center justify-between">
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 mr-2" />
                      提取结果
                    </div>
                    {subtitleResults.length > 0 && (
                      <Badge variant="secondary">{subtitleResults.length} 条</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    {subtitleResults.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        <Clock className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>暂无提取结果</p>
                        <p className="text-sm">请先开始字幕提取</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {subtitleResults.map((subtitle) => (
                          <Card
                            key={subtitle.id}
                            className={cn(
                              "p-3 cursor-pointer transition-colors hover:bg-gray-50",
                              selectedSubtitle === subtitle.id ? "ring-2 ring-blue-500" : ""
                            )}
                            onClick={() => {
                              setSelectedSubtitle(subtitle.id)
                              if (videoInfo) {
                                setVideoInfo(prev => prev ? { ...prev, currentTime: subtitle.startTime } : null)
                              }
                            }}
                          >
                            <div className="space-y-2">
                              <div className="text-xs text-gray-500">
                                {formatTime(subtitle.startTime)} → {formatTime(subtitle.endTime)}
                              </div>
                              <div className="text-sm">
                                {subtitle.text.map((line, index) => (
                                  <div key={index}>{line}</div>
                                ))}
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
