import * as ort from 'onnxruntime-web'

// OCR模型配置接口
export interface OCRModelConfig {
  detectionModelPath: string
  recognitionModelPath: string
  directionModelPath: string
  useGPU: boolean
  gpuDeviceId: number
  batchSize: number
}

// 文字检测结果
export interface TextDetection {
  bbox: [number, number, number, number] // [x1, y1, x2, y2]
  confidence: number
}

// 文字识别结果
export interface TextRecognition {
  text: string
  confidence: number
}

// 字幕锚点
export interface SubtitleAnchor {
  id: string
  centerX: number
  centerY: number
  height: number
  language: string
  isPrimary: boolean
  color: string
}

// 字幕结果
export interface SubtitleResult {
  id: string
  startTime: number
  endTime: number
  text: string[]
  anchorId: string
  confidence: number
}

// OCR处理器类
export class OCRProcessor {
  private detectionSession: ort.InferenceSession | null = null
  private recognitionSession: ort.InferenceSession | null = null
  private directionSession: ort.InferenceSession | null = null
  private config: OCRModelConfig | null = null
  private isInitialized = false

  constructor() {
    // 配置ONNX Runtime
    ort.env.wasm.wasmPaths = {
      'ort-wasm.wasm': '/onnx/ort-wasm.wasm',
      'ort-wasm-threaded.wasm': '/onnx/ort-wasm-threaded.wasm',
      'ort-wasm-simd.wasm': '/onnx/ort-wasm-simd.wasm',
      'ort-wasm-simd-threaded.wasm': '/onnx/ort-wasm-simd-threaded.wasm'
    }
  }

  // 初始化OCR模型
  async initializeModels(config: OCRModelConfig): Promise<void> {
    try {
      this.config = config
      
      // 设置执行提供者
      const providers: ort.ExecutionProvider[] = config.useGPU 
        ? ['webgl', 'wasm'] 
        : ['wasm']

      // 加载检测模型
      if (config.detectionModelPath) {
        console.log('Loading detection model...')
        this.detectionSession = await ort.InferenceSession.create(
          config.detectionModelPath,
          { executionProviders: providers }
        )
        console.log('Detection model loaded successfully')
      }

      // 加载识别模型
      if (config.recognitionModelPath) {
        console.log('Loading recognition model...')
        this.recognitionSession = await ort.InferenceSession.create(
          config.recognitionModelPath,
          { executionProviders: providers }
        )
        console.log('Recognition model loaded successfully')
      }

      // 加载方向分类模型
      if (config.directionModelPath) {
        console.log('Loading direction model...')
        this.directionSession = await ort.InferenceSession.create(
          config.directionModelPath,
          { executionProviders: providers }
        )
        console.log('Direction model loaded successfully')
      }

      this.isInitialized = true
      console.log('All OCR models initialized successfully')
    } catch (error) {
      console.error('Failed to initialize OCR models:', error)
      throw new Error(`OCR模型初始化失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 检查是否已初始化
  isReady(): boolean {
    return this.isInitialized && this.detectionSession !== null && this.recognitionSession !== null
  }

  // 从视频元素提取帧
  extractVideoFrame(video: HTMLVideoElement, currentTime: number): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      try {
        // 创建canvas来提取帧
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          reject(new Error('无法创建Canvas上下文'))
          return
        }

        // 设置canvas尺寸
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight

        // 设置视频时间
        video.currentTime = currentTime

        const onSeeked = () => {
          // 绘制当前帧到canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
          
          // 获取图像数据
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          
          video.removeEventListener('seeked', onSeeked)
          resolve(imageData)
        }

        video.addEventListener('seeked', onSeeked)
      } catch (error) {
        reject(error)
      }
    })
  }

  // 预处理图像数据
  private preprocessImage(imageData: ImageData, targetWidth = 640, targetHeight = 640): Float32Array {
    const { width, height, data } = imageData
    
    // 创建临时canvas进行缩放
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = targetWidth
    canvas.height = targetHeight
    
    // 创建ImageData并绘制
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')!
    tempCanvas.width = width
    tempCanvas.height = height
    tempCtx.putImageData(imageData, 0, 0)
    
    // 缩放图像
    ctx.drawImage(tempCanvas, 0, 0, width, height, 0, 0, targetWidth, targetHeight)
    
    // 获取缩放后的图像数据
    const scaledImageData = ctx.getImageData(0, 0, targetWidth, targetHeight)
    const scaledData = scaledImageData.data
    
    // 转换为Float32Array并归一化 (RGB -> BGR, 0-255 -> 0-1)
    const input = new Float32Array(3 * targetHeight * targetWidth)
    
    for (let i = 0; i < targetHeight * targetWidth; i++) {
      const pixelIndex = i * 4
      // BGR格式，归一化到0-1
      input[i] = scaledData[pixelIndex + 2] / 255.0 // B
      input[targetHeight * targetWidth + i] = scaledData[pixelIndex + 1] / 255.0 // G
      input[2 * targetHeight * targetWidth + i] = scaledData[pixelIndex] / 255.0 // R
    }
    
    return input
  }

  // 文字检测
  async detectText(imageData: ImageData): Promise<TextDetection[]> {
    if (!this.detectionSession) {
      throw new Error('检测模型未加载')
    }

    try {
      // 预处理图像
      const input = this.preprocessImage(imageData)
      
      // 创建输入张量
      const inputTensor = new ort.Tensor('float32', input, [1, 3, 640, 640])
      
      // 运行推理
      const results = await this.detectionSession.run({ input: inputTensor })
      
      // 解析检测结果
      const detections: TextDetection[] = []
      
      // 这里需要根据具体的检测模型输出格式来解析结果
      // 通常检测模型会输出边界框坐标和置信度
      // 这是一个示例实现，实际需要根据模型调整
      
      console.log('Detection results:', results)
      
      return detections
    } catch (error) {
      console.error('Text detection failed:', error)
      throw new Error(`文字检测失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 文字识别
  async recognizeText(imageData: ImageData, bbox: [number, number, number, number]): Promise<TextRecognition> {
    if (!this.recognitionSession) {
      throw new Error('识别模型未加载')
    }

    try {
      // 裁剪文字区域
      const croppedImage = this.cropImage(imageData, bbox)
      
      // 预处理图像
      const input = this.preprocessImage(croppedImage, 320, 32) // 识别模型通常使用不同的输入尺寸
      
      // 创建输入张量
      const inputTensor = new ort.Tensor('float32', input, [1, 3, 32, 320])
      
      // 运行推理
      const results = await this.recognitionSession.run({ input: inputTensor })
      
      // 解析识别结果
      // 这里需要根据具体的识别模型输出格式来解析结果
      // 通常识别模型会输出字符概率序列，需要进行CTC解码
      
      console.log('Recognition results:', results)
      
      return {
        text: '示例文字', // 实际需要从模型输出解析
        confidence: 0.9
      }
    } catch (error) {
      console.error('Text recognition failed:', error)
      throw new Error(`文字识别失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 裁剪图像
  private cropImage(imageData: ImageData, bbox: [number, number, number, number]): ImageData {
    const [x1, y1, x2, y2] = bbox
    const width = x2 - x1
    const height = y2 - y1
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = imageData.width
    canvas.height = imageData.height
    ctx.putImageData(imageData, 0, 0)
    
    const croppedCanvas = document.createElement('canvas')
    const croppedCtx = croppedCanvas.getContext('2d')!
    
    croppedCanvas.width = width
    croppedCanvas.height = height
    
    croppedCtx.drawImage(canvas, x1, y1, width, height, 0, 0, width, height)
    
    return croppedCtx.getImageData(0, 0, width, height)
  }

  // 处理视频字幕提取
  async processVideo(
    video: HTMLVideoElement,
    anchors: SubtitleAnchor[],
    fps: number,
    onProgress?: (progress: number, currentTime: number) => void
  ): Promise<SubtitleResult[]> {
    if (!this.isReady()) {
      throw new Error('OCR模型未准备就绪')
    }

    const results: SubtitleResult[] = []
    const duration = video.duration
    const interval = 1 / fps
    
    let currentTime = 0
    let frameCount = 0
    const totalFrames = Math.floor(duration * fps)

    while (currentTime < duration) {
      try {
        // 提取当前帧
        const imageData = await this.extractVideoFrame(video, currentTime)
        
        // 对每个锚点区域进行OCR
        for (const anchor of anchors) {
          // 根据锚点位置裁剪图像区域
          const bbox: [number, number, number, number] = [
            anchor.centerX - 100, // 简化的区域计算
            anchor.centerY - anchor.height / 2,
            anchor.centerX + 100,
            anchor.centerY + anchor.height / 2
          ]
          
          // 检测和识别文字
          const recognition = await this.recognizeText(imageData, bbox)
          
          if (recognition.text.trim() && recognition.confidence > 0.5) {
            results.push({
              id: `subtitle-${Date.now()}-${frameCount}`,
              startTime: currentTime,
              endTime: currentTime + interval,
              text: [recognition.text],
              anchorId: anchor.id,
              confidence: recognition.confidence
            })
          }
        }
        
        // 更新进度
        frameCount++
        const progress = (frameCount / totalFrames) * 100
        onProgress?.(progress, currentTime)
        
        currentTime += interval
      } catch (error) {
        console.error(`处理帧 ${currentTime}s 时出错:`, error)
        currentTime += interval
      }
    }

    return this.mergeSubtitleResults(results)
  }

  // 合并相邻的相同字幕
  private mergeSubtitleResults(results: SubtitleResult[]): SubtitleResult[] {
    if (results.length === 0) return []

    const merged: SubtitleResult[] = []
    let current = results[0]

    for (let i = 1; i < results.length; i++) {
      const next = results[i]
      
      // 如果是相同锚点的相同文字，且时间连续，则合并
      if (
        current.anchorId === next.anchorId &&
        current.text[0] === next.text[0] &&
        Math.abs(next.startTime - current.endTime) < 0.5
      ) {
        current.endTime = next.endTime
      } else {
        merged.push(current)
        current = next
      }
    }
    
    merged.push(current)
    return merged
  }

  // 释放资源
  async dispose(): Promise<void> {
    try {
      if (this.detectionSession) {
        await this.detectionSession.release()
        this.detectionSession = null
      }
      
      if (this.recognitionSession) {
        await this.recognitionSession.release()
        this.recognitionSession = null
      }
      
      if (this.directionSession) {
        await this.directionSession.release()
        this.directionSession = null
      }
      
      this.isInitialized = false
      console.log('OCR models disposed')
    } catch (error) {
      console.error('Error disposing OCR models:', error)
    }
  }
}
